<?php

namespace App\Http\Controllers;

use App\Http\Requests\ImportReportRequest;
use App\Http\Requests\FileUploadRequest;
use App\Services\ReportImportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

class ImportController extends Controller
{
    private ReportImportService $importService;

    public function __construct(ReportImportService $importService)
    {
        $this->importService = $importService;
    }

    /**
     * Display the import page with available files
     */
    public function index(): InertiaResponse
    {
        $availableFiles = $this->importService->getAvailableCsvFiles();
        $recentImports = $this->importService->getImportHistory([], 10);

        return Inertia::render('Import/Index', [
            'availableFiles' => $availableFiles,
            'recentImports' => $recentImports,
        ]);
    }

    /**
     * Import a specific CSV file
     */
    public function import(ImportReportRequest $request): JsonResponse
    {
        try {
            $filename = $request->validated('filename');
            $filePath = 'seed/' . $filename;

            // Perform the import
            $notification = $this->importService->importFromCsv($filePath);

            if ($notification->status === 'success') {
                return response()->json([
                    'success' => true,
                    'message' => "Successfully imported {$notification->records_imported} records from {$filename}",
                    'data' => [
                        'notification_id' => $notification->id,
                        'records_imported' => $notification->records_imported,
                        'report_code' => $notification->report_code,
                        'period' => $notification->period_name,
                        'processing_time' => $notification->import_details['processing_time_ms'] ?? 0,
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => "Import failed: {$notification->error_message}",
                    'data' => [
                        'notification_id' => $notification->id,
                        'error_message' => $notification->error_message,
                    ],
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('Import controller error', [
                'filename' => $request->validated('filename'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred during import',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get import history with filters
     */
    public function history(Request $request): JsonResponse
    {
        $filters = $request->only(['report_code', 'status', 'year', 'month']);
        $perPage = $request->get('per_page', 15);

        $history = $this->importService->getImportHistory($filters, $perPage);

        return response()->json([
            'success' => true,
            'data' => $history,
        ]);
    }

    /**
     * Get available CSV files
     */
    public function files(): JsonResponse
    {
        $files = $this->importService->getAvailableCsvFiles();

        return response()->json([
            'success' => true,
            'data' => $files,
        ]);
    }

    /**
     * Preview CSV file content (first few rows)
     */
    public function preview(Request $request): JsonResponse
    {
        $request->validate([
            'filename' => 'required|string',
        ]);

        try {
            $filename = $request->get('filename');
            $filePath = 'seed/' . $filename;

            $csvService = app(\App\Services\CsvImportService::class);
            $csvData = $csvService->parseCsv($filePath);

            // Return only first 5 rows for preview
            $previewData = array_slice($csvData['data'], 0, 5);

            return response()->json([
                'success' => true,
                'data' => [
                    'headers' => $csvData['headers'],
                    'preview_rows' => $previewData,
                    'total_rows' => $csvData['total_rows'],
                    'separator' => $csvData['separator'],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to preview file',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Upload a CSV file to the seed directory
     */
    public function upload(FileUploadRequest $request): JsonResponse
    {
        try {
            $file = $request->file('file');
            $filename = $file->getClientOriginalName();

            // Validate CSV headers for the report type
            $csvService = app(\App\Services\CsvImportService::class);
            $fileInfo = $csvService->parseFilename($filename);

            // Create a temporary file to validate CSV structure without loading entire file into memory
            $tempPath = $file->getRealPath();

            try {
                // Read and validate CSV headers without using Storage facade
                $handle = fopen($tempPath, 'r');
                if (!$handle) {
                    throw new \Exception('Unable to read uploaded file');
                }

                // Read first few lines to detect separator
                $lines = [];
                for ($i = 0; $i < 3 && !feof($handle); $i++) {
                    $line = fgets($handle);
                    if ($line !== false && !empty(trim($line))) {
                        $lines[] = trim($line);
                    }
                }
                rewind($handle);

                if (empty($lines)) {
                    fclose($handle);
                    throw new \Exception('File appears to be empty');
                }

                // Detect separator manually
                $separators = [';', ',', "\t", '|'];
                $separatorCounts = [];

                foreach ($separators as $separator) {
                    $counts = [];
                    foreach ($lines as $line) {
                        $counts[] = substr_count($line, $separator);
                    }

                    // Check if separator appears consistently across lines
                    $uniqueCounts = array_unique($counts);
                    if (count($uniqueCounts) === 1 && $counts[0] > 0) {
                        $separatorCounts[$separator] = $counts[0];
                    }
                }

                if (empty($separatorCounts)) {
                    fclose($handle);
                    throw new \Exception("Could not detect CSV separator. Please ensure the file uses one of: " . implode(', ', $separators));
                }

                // Get separator with highest count (most columns)
                $separator = array_search(max($separatorCounts), $separatorCounts);

                // Read first line (headers)
                $headerLine = fgets($handle);
                fclose($handle);

                if (!$headerLine) {
                    throw new \Exception('File appears to be empty');
                }

                // Parse headers
                $headers = str_getcsv(trim($headerLine), $separator);

                // Remove BOM if present
                if (!empty($headers[0])) {
                    $headers[0] = preg_replace('/^\xEF\xBB\xBF/', '', $headers[0]);
                }

                $csvService->validateCsvStructure($headers, $fileInfo['report_code']);

            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid CSV structure: ' . $e->getMessage(),
                ], 422);
            }

            // Store file using Laravel's storage system (which handles streaming automatically)
            $storedPath = $file->storeAs('seed', $filename);

            if (!$storedPath) {
                throw new \Exception('Failed to store uploaded file');
            }

            // Log successful upload
            Log::info('File uploaded successfully', [
                'filename' => $filename,
                'size' => $file->getSize(),
                'report_code' => $fileInfo['report_code'],
                'period' => sprintf('%02d/%d', $fileInfo['month'], $fileInfo['year']),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'size' => $file->getSize(),
                    'report_code' => $fileInfo['report_code'],
                    'period' => sprintf('%02d/%d', $fileInfo['month'], $fileInfo['year']),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Upload error', [
                'filename' => $request->file('file')?->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage(),
            ], 500);
        }
    }
}
