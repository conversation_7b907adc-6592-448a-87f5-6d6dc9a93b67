<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Storage;

class FileUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:csv,txt',
                'max:102400', // Max 100MB
                function ($attribute, $value, $fail) {
                    if (!$value) {
                        return;
                    }

                    $filename = $value->getClientOriginalName();
                    
                    // Validate filename pattern
                    if (!preg_match('/^[A-Z]\d{2}_\d{6}\.csv$/i', $filename)) {
                        $fail('Invalid filename format. Must match pattern: {report_code}_{MMYYYY}.csv (e.g., A01_042025.csv)');
                        return;
                    }

                    // Check if file already exists
                    $targetPath = 'seed/' . $filename;
                    if (Storage::exists($targetPath)) {
                        $fail('File already exists. Please rename your file or delete the existing one first.');
                        return;
                    }

                    // Validate file is not empty
                    if ($value->getSize() === 0) {
                        $fail('The uploaded file is empty.');
                        return;
                    }
                },
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'Please select a file to upload.',
            'file.file' => 'The uploaded item must be a valid file.',
            'file.mimes' => 'Only CSV files are allowed.',
            'file.max' => 'The file size must not exceed 100MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file' => 'CSV file',
        ];
    }
}
