import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  VisibilityState,
  PaginationState,
} from "@tanstack/react-table";
import { useState, useEffect } from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "./data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";

interface PaginationOptions {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  siblingsCount?: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  filterColumn?: string;
  filterPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  onSearchSubmit?: () => void;
  typeFilter?: string;
  onTypeFilterChange?: (value: string) => void;
  pagination?: PaginationOptions;
  showPagination?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  filterColumn,
  filterPlaceholder,
  searchValue,
  onSearchChange,
  onSearchSubmit,
  typeFilter,
  onTypeFilterChange,
  pagination,
  showPagination = true
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({
    pageIndex: pagination ? pagination.currentPage - 1 : 0,
    pageSize: pagination ? pagination.pageSize : 10,
  });

  // Update pagination state when props change
  useEffect(() => {
    if (pagination) {
      setPagination({
        pageIndex: pagination.currentPage - 1,
        pageSize: pagination.pageSize,
      });
    }
  }, [pagination?.currentPage, pagination?.pageSize]);

  // Handle client-side column filter changes
  const handleColumnFilterChange = (value: string) => {
    if (filterColumn && onSearchChange) {
      onSearchChange(value);
    }
  };

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: !!pagination,
    pageCount: pagination?.totalPages || -1,
    onPaginationChange: setPagination,
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex,
        pageSize,
      },
    },
  });

  // Handle page change
  useEffect(() => {
    if (pagination && (pageIndex !== pagination.currentPage - 1 || pageSize !== pagination.pageSize)) {
      if (pageSize !== pagination.pageSize) {
        pagination.onPageSizeChange(pageSize);
      } else {
        pagination.onPageChange(pageIndex + 1);
      }
    }
  }, [pageIndex, pageSize]);

  return (
    <div className="space-y-4">
      {filterColumn && (
        <DataTableToolbar
          table={table}
          filterColumn={filterColumn}
          filterPlaceholder={filterPlaceholder}
          searchValue={searchValue}
          onSearchChange={handleColumnFilterChange}
          onSearchSubmit={onSearchSubmit}
          typeFilter={typeFilter}
          onTypeFilterChange={onTypeFilterChange}
        />
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {showPagination && <DataTablePagination
        table={table}
        serverPagination={pagination ? {
          currentPage: pagination.currentPage,
          totalPages: pagination.totalPages,
          totalItems: pagination.totalItems,
        } : undefined}
      />}
    </div>
  );
}
