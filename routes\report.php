<?php

use App\Http\Controllers\ImportController;
use App\Http\Controllers\ReportBatchController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard route
    Route::get('/dashboard', [ReportBatchController::class, 'dashboard'])
        ->name('dashboard');

    // Resource routes for report batches
    Route::resource('report', ReportBatchController::class);

    // Additional action routes
    Route::prefix('report/{reportBatch}')->group(function () {
        // Data validation and generation
        Route::post('/validate-data', [ReportBatchController::class, 'validateData'])
            ->name('report.validate-data');
        
        Route::post('/generate-data', [ReportBatchController::class, 'generateData'])
            ->name('report.generate-data');

        // Export routes
        Route::get('/export/json', [ReportBatchController::class, 'exportJson'])
            ->name('report.export.json');
        
        Route::get('/export/csv', [ReportBatchController::class, 'exportCsv'])
            ->name('report.export.csv');
    });

    // Bulk operations
    Route::post('/report-bulk/export', [ReportBatchController::class, 'bulkExport'])
        ->name('report.bulk.export');

    // Import routes
    Route::prefix('import')->name('import.')->group(function () {
        Route::get('/', [ImportController::class, 'index'])
            ->name('index');

        Route::post('/file', [ImportController::class, 'import'])
            ->name('file');

        Route::post('/upload', [ImportController::class, 'upload'])
            ->name('upload');

        Route::get('/history', [ImportController::class, 'history'])
            ->name('history');

        Route::get('/files', [ImportController::class, 'files'])
            ->name('files');

        Route::post('/preview', [ImportController::class, 'preview'])
            ->name('preview');
    });
});
