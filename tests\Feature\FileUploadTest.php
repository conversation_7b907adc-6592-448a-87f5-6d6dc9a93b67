<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class FileUploadTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    public function test_upload_valid_csv_file_succeeds()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a valid CSV file with correct A01 headers
        $csvContent = "FLAG DETAIL,KODE REGISTER / NOMOR AGUNAN,NOMOR REKENING FASILITAS,NOMOR CIF DEBITUR\n";
        $csvContent .= "D,001,1234567890,CIF001\n";
        $csvContent .= "D,002,0987654321,CIF002\n";

        $file = UploadedFile::fake()->createWithContent('A01_042025.csv', $csvContent);

        $response = $this->postJson('/import/upload', [
            'file' => $file,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'filename',
                        'size',
                        'report_code',
                        'period',
                    ]
                ]);

        // Verify file was stored
        Storage::assertExists('seed/A01_042025.csv');
    }

    public function test_upload_invalid_filename_fails()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $csvContent = "HEADER1,HEADER2\nvalue1,value2\n";
        $file = UploadedFile::fake()->createWithContent('invalid_filename.csv', $csvContent);

        $response = $this->postJson('/import/upload', [
            'file' => $file,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_upload_non_csv_file_fails()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $file = UploadedFile::fake()->create('A01_042025.txt', 100);

        $response = $this->postJson('/import/upload', [
            'file' => $file,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_upload_empty_file_fails()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $file = UploadedFile::fake()->create('A01_042025.csv', 0);

        $response = $this->postJson('/import/upload', [
            'file' => $file,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_upload_duplicate_file_fails()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create existing file
        Storage::put('seed/A01_042025.csv', 'existing content');

        $csvContent = "HEADER1,HEADER2\nvalue1,value2\n";
        $file = UploadedFile::fake()->createWithContent('A01_042025.csv', $csvContent);

        $response = $this->postJson('/import/upload', [
            'file' => $file,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_upload_large_file_fails()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a file larger than 100MB (100MB = 102400KB)
        $file = UploadedFile::fake()->create('A01_042025.csv', 102401);

        $response = $this->postJson('/import/upload', [
            'file' => $file,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_upload_requires_authentication()
    {
        $csvContent = "HEADER1,HEADER2\nvalue1,value2\n";
        $file = UploadedFile::fake()->createWithContent('A01_042025.csv', $csvContent);

        $response = $this->postJson('/import/upload', [
            'file' => $file,
        ]);

        $response->assertStatus(401);
    }
}
